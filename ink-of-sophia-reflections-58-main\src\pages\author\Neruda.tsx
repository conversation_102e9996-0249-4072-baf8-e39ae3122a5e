import Navigation from "@/components/Navigation";
import AuthorHero from "@/components/AuthorHero";
import BiographySection from "@/components/BiographySection";
import WorksSection from "@/components/WorksSection";
import QuotesSection from "@/components/QuotesSection";

export default function <PERSON><PERSON><PERSON>() {
  const biography = [
    "<PERSON> (1904-1973) was a Chilean poet and diplomat who became one of the most influential poets of the 20th century. Born <PERSON>, he adopted his pen name in honor of the Czech poet <PERSON>.",
    "<PERSON><PERSON><PERSON>'s poetry evolved through several distinct phases, from the melancholic romanticism of his early work to the surrealist period, and finally to a more accessible, politically engaged style. His work often celebrated love, nature, and the common people of Latin America.",
    "A committed communist and diplomat, <PERSON><PERSON><PERSON> served as a senator and consul in various countries. He was awarded the Nobel Prize in Literature in 1971, and his death in 1973, shortly after <PERSON><PERSON><PERSON>'s coup in Chile, remains controversial and subject to investigation."
  ];

  const works = [
    { title: "Twenty Love Poems and a Song of Despair", year: 1924, description: "Early collection of passionate love poetry" },
    { title: "Residence on Earth", year: 1935, description: "Surrealist poetry exploring existential themes" },
    { title: "Canto General", year: 1950, description: "Epic poem celebrating Latin American history and landscape" },
    { title: "Elementary Odes", year: 1954, description: "Simple, celebratory poems about everyday objects" },
    { title: "100 Love Sonnets", year: 1959, description: "Classical sonnets dedicated to his wife Matilde" }
  ];

  const quotes = [
    { text: "Love is so short, forgetting is so long." },
    { text: "You can cut all the flowers but you cannot keep spring from coming." },
    { text: "I love you without knowing how, or when, or from where. I love you straightforwardly, without complexities or pride." }
  ];

  return (
    <div className="min-h-screen bg-cream">
      <Navigation />
      <AuthorHero 
        name="Pablo Neruda" 
        quote="Tonight I can write the saddest lines." 
        years="1904-1973" 
      />
      <BiographySection title="Biography" content={biography} />
      <WorksSection works={works} />
      <QuotesSection quotes={quotes} />
    </div>
  );
}