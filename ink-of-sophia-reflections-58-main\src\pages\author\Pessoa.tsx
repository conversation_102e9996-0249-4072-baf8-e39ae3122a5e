import Navigation from "@/components/Navigation";
import AuthorHero from "@/components/AuthorHero";
import BiographySection from "@/components/BiographySection";
import WorksSection from "@/components/WorksSection";
import QuotesSection from "@/components/QuotesSection";

export default function <PERSON><PERSON>oa() {
  const biography = [
    "<PERSON> (1888-1935) was a Portuguese poet, writer, literary critic, translator, publisher, and philosopher who is considered one of the most significant literary figures of the 20th century and one of the greatest poets in the Portuguese language.",
    "<PERSON><PERSON><PERSON> is famous for his creation of heteronyms - fully developed fictional poets with their own biographies, philosophies, and writing styles. His most famous heteronyms include <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, each representing different aspects of the human condition and literary expression.",
    "Despite writing prolifically, most of <PERSON><PERSON><PERSON>'s work was unpublished during his lifetime, discovered in a trunk after his death. His innovative approach to identity, his philosophical depth, and his modernist techniques have influenced countless writers and established him as a cornerstone of Portuguese literature."
  ];

  const works = [
    { title: "Message", year: 1934, description: "Epic poem celebrating Portuguese history and identity" },
    { title: "The Book of Disquiet", year: 1982, description: "Philosophical fragments published posthumously" },
    { title: "<PERSON>: The Keeper of Sheep", year: 1925, description: "Poetry by <PERSON><PERSON><PERSON>'s most important heteronym" },
    { title: "<PERSON>is: Odes", year: 1946, description: "Classical poetry exploring stoicism and fate" },
    { title: "Álvaro de Campos: Poems", year: 1944, description: "Futurist poetry celebrating modernity and sensation" }
  ];

  const quotes = [
    { text: "I am nothing. I shall always be nothing. I cannot want to be nothing. Apart from that, I have in me all the dreams of the world." },
    { text: "To be great, be entire; exclude nothing, exaggerate nothing that is not you." },
    { text: "Literature is the most agreeable way of ignoring life." }
  ];

  return (
    <div className="min-h-screen bg-cream">
      <Navigation />
      <AuthorHero 
        name="Fernando Pessoa" 
        quote="I have more souls than one." 
        years="1888-1935" 
      />
      <BiographySection title="Biography" content={biography} />
      <WorksSection works={works} />
      <QuotesSection quotes={quotes} />
    </div>
  );
}