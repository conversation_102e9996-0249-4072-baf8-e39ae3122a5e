import Navigation from "@/components/Navigation";
import AuthorHero from "@/components/AuthorHero";
import BiographySection from "@/components/BiographySection";
import WorksSection from "@/components/WorksSection";
import QuotesSection from "@/components/QuotesSection";

export default function <PERSON><PERSON><PERSON><PERSON>() {
  const biography = [
    "<PERSON> (1828-1910) was a Russian writer widely regarded as one of the greatest authors of all time. Born into an aristocratic family, he would later renounce his privileged lifestyle to pursue a simpler, more spiritual existence based on his moral and religious beliefs.",
    "<PERSON><PERSON><PERSON><PERSON>'s early works were largely autobiographical, but he achieved worldwide fame with his epic novels 'War and Peace' and 'Anna Karen<PERSON>.' These works explore complex themes of war, peace, love, family, and faith while providing panoramic views of Russian society.",
    "In his later years, <PERSON><PERSON><PERSON><PERSON> underwent a spiritual crisis that led him to develop his own interpretation of Christianity, emphasizing nonviolence, simplicity, and moral purification. His philosophical writings and advocacy for social reform influenced figures like <PERSON><PERSON><PERSON> and <PERSON>."
  ];

  const works = [
    { title: "War and Peace", year: 1869, description: "Epic novel set during the Napoleonic Wars" },
    { title: "<PERSON> Karenina", year: 1878, description: "Tragic story of love, society, and moral conflict" },
    { title: "The Death of Ivan Il<PERSON>ch", year: 1886, description: "<PERSON><PERSON> exploring mortality and the meaning of life" },
    { title: "Resurrection", year: 1899, description: "Novel about moral and spiritual redemption" },
    { title: "A Confession", year: 1882, description: "Autobiographical work on his spiritual crisis" }
  ];

  const quotes = [
    { text: "Everyone thinks of changing the world, but no one thinks of changing himself." },
    { text: "All happy families are alike; each unhappy family is unhappy in its own way." },
    { text: "The two most powerful warriors are patience and time." }
  ];

  return (
    <div className="min-h-screen bg-cream">
      <Navigation />
      <AuthorHero 
        name="Leo Tolstoy" 
        quote="If you want to be happy, be." 
        years="1828-1910" 
      />
      <BiographySection title="Biography" content={biography} />
      <WorksSection works={works} />
      <QuotesSection quotes={quotes} />
    </div>
  );
}