interface Work {
  title: string;
  year: number;
  description: string;
}

interface WorksSectionProps {
  works: Work[];
}

const WorksSection = ({ works }: WorksSectionProps) => {
  return (
    <section className="py-16 lg:py-24 bg-gradient-to-br from-background via-secondary/20 to-accent/10">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16 animate-fade-in">
          <h2 className="font-heading text-heading font-bold text-primary mb-4 tracking-tight">
            Featured Works
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-primary via-accent to-primary mx-auto rounded-full opacity-60"></div>
        </div>
        
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8">
          {works.map((work, index) => (
            <div 
              key={work.title}
              className="group relative bg-card/80 backdrop-blur-sm rounded-2xl p-8 shadow-soft hover:shadow-elegant transition-all duration-500 animate-scale-in border border-border/30 hover:border-primary/20 hover:bg-card/90 overflow-hidden"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              {/* Decorative gradient overlay */}
              <div className="absolute inset-0 bg-gradient-to-br from-accent/5 via-transparent to-primary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-2xl"></div>
              
              {/* Content */}
              <div className="relative z-10">
                <div className="flex items-start justify-between mb-6">
                  <h3 className="font-heading text-xl font-semibold text-primary leading-tight group-hover:text-primary/90 transition-colors duration-300 pr-4">
                    {work.title}
                  </h3>
                  <div className="flex-shrink-0">
                    <span className="inline-flex items-center justify-center w-12 h-8 text-xs font-medium text-primary bg-gradient-to-r from-accent/30 to-secondary/40 rounded-full border border-primary/10">
                      {work.year}
                    </span>
                  </div>
                </div>
                
                <p className="font-body text-foreground/70 leading-relaxed text-sm group-hover:text-foreground/85 transition-colors duration-300">
                  {work.description}
                </p>
                
                {/* Decorative bottom accent */}
                <div className="mt-6 h-px bg-gradient-to-r from-transparent via-primary/20 to-transparent"></div>
              </div>
              
              {/* Corner decoration */}
              <div className="absolute top-4 right-4 w-2 h-2 bg-accent/40 rounded-full opacity-60 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default WorksSection;