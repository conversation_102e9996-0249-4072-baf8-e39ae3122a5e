import Navigation from "@/components/Navigation";
import AuthorHero from "@/components/AuthorHero";
import BiographySection from "@/components/BiographySection";
import WorksSection from "@/components/WorksSection";
import QuotesSection from "@/components/QuotesSection";

export default function Achebe() {
  const biography = [
    "<PERSON><PERSON> (1930-2013) was a Nigerian novelist, poet, professor, and critic who is widely regarded as the most important African writer of his generation. Born <PERSON> in Ogidi, Nigeria, he played a crucial role in developing modern African literature.",
    "<PERSON><PERSON><PERSON>'s groundbreaking novel 'Things Fall Apart' became one of the most widely read works of African literature, offering a complex portrayal of pre-colonial Igbo society and the impact of European colonialism. His work challenged Western stereotypes about Africa and gave voice to African perspectives on their own history and culture.",
    "Beyond his literary achievements, <PERSON><PERSON><PERSON> was a passionate advocate for African identity and cultural dignity. As a teacher, critic, and cultural commentator, he inspired generations of African writers and helped establish African literature as a vital force in world literature."
  ];

  const works = [
    { title: "Things Fall Apart", year: 1958, description: "Landmark novel about colonialism's impact on Igbo society" },
    { title: "No Longer at Ease", year: 1960, description: "<PERSON><PERSON> exploring post-colonial Nigerian society" },
    { title: "Arrow of God", year: 1964, description: "Novel about conflict between traditional beliefs and colonial rule" },
    { title: "A Man of the People", year: 1966, description: "Political satire about corruption in post-independence Nigeria" },
    { title: "The Education of a British-Protected Child", year: 2009, description: "Collection of essays on literature, politics, and culture" }
  ];

  const quotes = [
    { text: "Until the lions have their own historians, the history of the hunt will always glorify the hunter." },
    { text: "The white man is very clever. He came quietly and peaceably with his religion. We were amused at his foolishness and allowed him to stay." },
    { text: "A man who calls his kinsmen to a feast does not do so to save them from starving. They all have food in their own homes." }
  ];

  return (
    <div className="min-h-screen bg-cream">
      <Navigation />
      <AuthorHero 
        name="Chinua Achebe" 
        quote="If you don't like someone's story, write your own." 
        years="1930-2013" 
      />
      <BiographySection title="Biography" content={biography} />
      <WorksSection works={works} />
      <QuotesSection quotes={quotes} />
    </div>
  );
}