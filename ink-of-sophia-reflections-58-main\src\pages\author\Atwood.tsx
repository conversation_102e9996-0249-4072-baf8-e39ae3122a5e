import Navigation from "@/components/Navigation";
import AuthorHero from "@/components/AuthorHero";
import BiographySection from "@/components/BiographySection";
import WorksSection from "@/components/WorksSection";
import QuotesSection from "@/components/QuotesSection";

export default function <PERSON><PERSON>() {
  const biography = [
    "<PERSON> (born 1939) is a Canadian poet, novelist, literary critic, essayist, teacher, environmental activist, and inventor. She is one of Canada's most celebrated authors and a leading voice in contemporary literature, known for her speculative fiction and feminist themes.",
    "<PERSON><PERSON>'s work often explores themes of power, gender, identity, and environmental concerns through the lens of speculative fiction. Her dystopian novel 'The Handmaid's Tale' has become particularly relevant in contemporary political discourse, inspiring television adaptations and political movements worldwide.",
    "With over fifty books of poetry, fiction, and non-fiction to her name, <PERSON><PERSON> has received numerous awards including the Booker Prize and has been shortlisted multiple times. Her prescient vision of future societies and her masterful storytelling have made her one of the most important writers of our time."
  ];

  const works = [
    { title: "The Handmaid's Tale", year: 1985, description: "Dystopian novel about reproductive oppression in a totalitarian state" },
    { title: "Cat's Eye", year: 1988, description: "Coming-of-age novel exploring female friendship and artistic identity" },
    { title: "The Blind Assassin", year: 2000, description: "Booker Prize-winning novel within a novel" },
    { title: "Oryx and Crake", year: 2003, description: "Post-apocalyptic novel beginning the MaddAddam trilogy" },
    { title: "The Testaments", year: 2019, description: "Sequel to The Handmaid's Tale, winner of the Booker Prize" }
  ];

  const quotes = [
    { text: "A word after a word after a word is power." },
    { text: "Better never means better for everyone... It always means worse, for some." },
    { text: "We were the people who were not in the papers. We lived in the blank white spaces at the edges of print." }
  ];

  return (
    <div className="min-h-screen bg-cream">
      <Navigation />
      <AuthorHero 
        name="Margaret Atwood" 
        quote="Ignoring isn't the same as ignorance, you have to work at it." 
        years="1939-" 
      />
      <BiographySection title="Biography" content={biography} />
      <WorksSection works={works} />
      <QuotesSection quotes={quotes} />
    </div>
  );
}