import Navigation from "@/components/Navigation";
import AuthorHero from "@/components/AuthorHero";
import BiographySection from "@/components/BiographySection";
import WorksSection from "@/components/WorksSection";
import QuotesSection from "@/components/QuotesSection";

export default function <PERSON><PERSON>() {
  const biography = [
    "<PERSON> (1903-1950), born <PERSON>, was an English novelist, essayist, journalist, and critic whose work is characterized by lucid prose, social criticism, and opposition to totalitarianism. His experiences as an imperial police officer in Burma and his observations of social inequality shaped his political consciousness.",
    "<PERSON><PERSON>'s most famous works, '1984' and 'Animal Farm,' serve as powerful warnings against totalitarian regimes and the manipulation of truth. His concept of 'doublethink,' 'newspeak,' and 'Big Brother' has entered common usage, demonstrating the enduring relevance of his dystopian vision.",
    "Beyond fiction, <PERSON><PERSON> was a masterful essayist whose works like 'Politics and the English Language' continue to influence writers and thinkers. His commitment to democratic socialism and his belief in the importance of clear, honest language made him one of the most influential political writers of the 20th century."
  ];

  const works = [
    { title: "Animal Farm", year: 1945, description: "Allegorical fable about the Russian Revolution and Stalinism" },
    { title: "1984", year: 1949, description: "Dystopian masterpiece about totalitarian surveillance and control" },
    { title: "Down and Out in Paris and London", year: 1933, description: "Memoir of poverty and social observation" },
    { title: "The Road to Wigan Pier", year: 1937, description: "Investigation into working-class conditions in northern England" },
    { title: "Homage to Catalonia", year: 1938, description: "Personal account of fighting in the Spanish Civil War" }
  ];

  const quotes = [
    { text: "War is peace. Freedom is slavery. Ignorance is strength." },
    { text: "In a time of deceit telling the truth is a revolutionary act." },
    { text: "All animals are equal, but some animals are more equal than others." }
  ];

  return (
    <div className="min-h-screen bg-cream">
      <Navigation />
      <AuthorHero 
        name="George Orwell" 
        quote="If you want a picture of the future, imagine a boot stamping on a human face—forever." 
        years="1903-1950" 
      />
      <BiographySection title="Biography" content={biography} />
      <WorksSection works={works} />
      <QuotesSection quotes={quotes} />
    </div>
  );
}