import { useState } from "react";
import { Search as SearchIcon } from "lucide-react";
import { Link } from "react-router-dom";
import Navigation from "@/components/Navigation";

const searchData = [
  // Authors
  { type: "author", title: "<PERSON><PERSON><PERSON>", url: "/author/<PERSON><PERSON><PERSON><PERSON>", description: "Russian novelist and philosopher" },
  { type: "author", title: "<PERSON>", url: "/author/kafka", description: "Czech writer known for surreal fiction" },
  { type: "author", title: "<PERSON>hov", url: "/author/chekhov", description: "Russian playwright and short story writer" },
  { type: "author", title: "<PERSON>", url: "/author/wilde", description: "Irish poet and playwright" },
  { type: "author", title: "<PERSON>", url: "/author/plath", description: "American poet and novelist" },
  { type: "author", title: "William <PERSON>", url: "/author/shakespeare", description: "English playwright and poet" },
  
  // Pages
  { type: "page", title: "Quotes", url: "/quotes", description: "Collection of literary quotes" },
  { type: "page", title: "About", url: "/about", description: "About Ink of Sophia" },
  { type: "page", title: "Authors", url: "/authors", description: "Browse all authors" },
  
  // Keywords
  { type: "keyword", title: "Russian Literature", url: "/authors", description: "Dostoevsky, Chekhov, and more" },
  { type: "keyword", title: "Poetry", url: "/authors", description: "Plath, Shakespeare, and others" },
  { type: "keyword", title: "Philosophy", url: "/quotes", description: "Philosophical quotes and insights" },
];

export default function Search() {
  const [query, setQuery] = useState("");
  const [results, setResults] = useState<typeof searchData>([]);

  const handleSearch = (searchQuery: string) => {
    setQuery(searchQuery);
    
    if (searchQuery.trim() === "") {
      setResults([]);
      return;
    }

    const filtered = searchData.filter(item =>
      item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.description.toLowerCase().includes(searchQuery.toLowerCase())
    );
    
    setResults(filtered);
  };

  return (
    <div className="min-h-screen bg-cream">
      <Navigation />
      
      <div className="container mx-auto px-4 py-12">
        <div className="max-w-4xl mx-auto">
          <header className="text-center mb-16 animate-fade-in">
            <h1 className="font-display text-4xl md:text-6xl text-charcoal mb-6">
              Search
            </h1>
            <p className="text-lg text-brown max-w-2xl mx-auto leading-relaxed mb-8">
              Discover authors, quotes, and literary insights across our archive.
            </p>
            
            <div className="relative max-w-2xl mx-auto">
              <SearchIcon className="absolute left-4 top-1/2 transform -translate-y-1/2 text-brown h-5 w-5" />
              <input
                type="text"
                placeholder="Search for authors, quotes, or topics..."
                value={query}
                onChange={(e) => handleSearch(e.target.value)}
                className="w-full pl-12 pr-4 py-4 text-lg border border-brown/20 rounded-lg bg-white/60 backdrop-blur-sm focus:outline-none focus:border-brown/40 focus:ring-2 focus:ring-brown/20"
              />
            </div>
          </header>

          {query && (
            <div className="animate-fade-in">
              <h2 className="text-2xl font-display text-charcoal mb-6">
                Search Results for "{query}"
              </h2>
              
              {results.length === 0 ? (
                <div className="bg-white/60 backdrop-blur-sm rounded-lg p-8 shadow-soft border border-white/20 text-center">
                  <p className="text-brown text-lg">
                    No results found. Try searching for an author name, literary term, or topic.
                  </p>
                </div>
              ) : (
                <div className="grid gap-4">
                  {results.map((result, index) => (
                    <Link
                      key={index}
                      to={result.url}
                      className="block bg-white/60 backdrop-blur-sm rounded-lg p-6 shadow-soft border border-white/20 hover:bg-white/80 transition-all duration-300 hover:shadow-lg"
                    >
                      <div className="flex items-start justify-between">
                        <div>
                          <h3 className="text-xl font-display text-charcoal mb-2">
                            {result.title}
                          </h3>
                          <p className="text-brown">
                            {result.description}
                          </p>
                        </div>
                        <span className="px-3 py-1 bg-pale-pink/60 text-brown text-sm rounded-full capitalize">
                          {result.type}
                        </span>
                      </div>
                    </Link>
                  ))}
                </div>
              )}
            </div>
          )}

          {!query && (
            <div className="animate-fade-in">
              <h2 className="text-2xl font-display text-charcoal mb-6 text-center">
                Popular Searches
              </h2>
              
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                {["Dostoevsky", "Kafka", "Philosophy", "Poetry", "Russian Literature", "Quotes"].map((term, index) => (
                  <button
                    key={index}
                    onClick={() => handleSearch(term)}
                    className="bg-white/60 backdrop-blur-sm rounded-lg p-4 shadow-soft border border-white/20 hover:bg-white/80 transition-all duration-300 text-charcoal hover:text-brown"
                  >
                    {term}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}