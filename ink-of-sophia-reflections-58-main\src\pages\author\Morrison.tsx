import Navigation from "@/components/Navigation";
import AuthorHero from "@/components/AuthorHero";
import BiographySection from "@/components/BiographySection";
import WorksSection from "@/components/WorksSection";
import QuotesSection from "@/components/QuotesSection";

export default function Morrison() {
  const biography = [
    "<PERSON> (1931-2019), born <PERSON>, was an American novelist, essayist, and editor who became the first African American woman to win the Nobel Prize in Literature in 1993. Her powerful novels explored the African American experience with unprecedented depth and artistry.",
    "<PERSON>'s writing is characterized by its lyrical prose, complex narrative structures, and unflinching examination of slavery's legacy and its impact on individual and collective memory. She gave voice to previously silenced stories, particularly those of African American women, with extraordinary compassion and literary skill.",
    "Beyond her literary achievements, <PERSON> was a distinguished editor at Random House, where she championed works by African American authors. Her influence on American literature is immeasurable, as she opened new paths for understanding history, identity, and the enduring power of storytelling."
  ];

  const works = [
    { title: "The Bluest Eye", year: 1970, description: "Debut novel exploring beauty standards and self-worth" },
    { title: "<PERSON><PERSON>", year: 1973, description: "Complex portrait of friendship between two African American women" },
    { title: "Song of Solomon", year: 1977, description: "Epic family saga blending realism with African American folklore" },
    { title: "Beloved", year: 1987, description: "Pulitzer Prize-winning masterpiece about slavery's haunting legacy" },
    { title: "Jazz", year: 1992, description: "Musical narrative set in 1920s Harlem" },
    { title: "Paradise", year: 1997, description: "Ambitious novel about an all-black town in 1970s Oklahoma" }
  ];

  const quotes = [
    { text: "If you want to fly, you have to give up the things that weigh you down." },
    { text: "The function, the very serious function of racism is distraction." },
    { text: "Freeing yourself was one thing, claiming ownership of that freed self was another." }
  ];

  return (
    <div className="min-h-screen bg-cream">
      <Navigation />
      <AuthorHero 
        name="Toni Morrison" 
        quote="If there's a book that you want to read, but it hasn't been written yet, then you must write it." 
        years="1931-2019" 
      />
      <BiographySection title="Biography" content={biography} />
      <WorksSection works={works} />
      <QuotesSection quotes={quotes} />
    </div>
  );
}