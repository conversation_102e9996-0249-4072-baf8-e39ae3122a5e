import Navigation from "@/components/Navigation";
import AuthorHero from "@/components/AuthorHero";
import BiographySection from "@/components/BiographySection";
import WorksSection from "@/components/WorksSection";
import QuotesSection from "@/components/QuotesSection";

const Kafka = () => {
  const biography = [
    "<PERSON> was born in Prague in 1883 to a middle-class Jewish family. His relationship with his domineering father deeply influenced his work, creating themes of alienation, guilt, and the struggle against incomprehensible authority that would define his literary voice. Despite studying law and working in insurance, <PERSON><PERSON><PERSON>'s true passion lay in writing.",
    "Most of <PERSON><PERSON><PERSON>'s major works were published posthumously, as he had instructed his friend <PERSON> to destroy his manuscripts. Fortunately, <PERSON><PERSON> disobeyed this request, preserving for posterity some of the most influential literature of the 20th century. <PERSON><PERSON><PERSON>'s writing style, characterized by surreal situations and bureaucratic nightmares, gave birth to the term 'Kafkaesque.'",
    "<PERSON><PERSON><PERSON> died young at 40 from tuberculosis, leaving behind a body of work that would profoundly influence existentialist philosophy and modernist literature. His exploration of absurdity, anxiety, and the human condition resonates deeply with readers facing the complexities of modern life."
  ];

  const works = [
    {
      title: "The Metamorphosis",
      year: 1915,
      description: "The story of <PERSON>, who wakes up transformed into a giant insect, exploring themes of alienation and family obligation."
    },
    {
      title: "The Trial",
      year: 1925,
      description: "<PERSON> <PERSON>. is arrested and prosecuted by an inaccessible authority for an unspecified crime, a nightmarish vision of bureaucracy."
    },
    {
      title: "The Castle",
      year: 1926,
      description: "A land surveyor's futile attempts to gain access to a mysterious castle, representing the search for meaning and belonging."
    },
    {
      title: "<PERSON>erika",
      year: 1927,
      description: "The story of young Karl Rossmann's experiences in America, a satirical view of the promised land of opportunity."
    },
    {
      title: "A Hunger Artist",
      year: 1922,
      description: "A disturbing tale of a professional faster whose art becomes increasingly meaningless to society."
    }
  ];

  const quotes = [
    {
      text: "I cannot make you understand. I cannot make anyone understand what is happening inside me. I cannot even explain it to myself.",
      source: "The Metamorphosis"
    },
    {
      text: "A book must be the axe for the frozen sea inside us.",
    },
    {
      text: "Don't bend; don't water it down; don't try to make it logical; don't edit your own soul according to the fashion. Rather, follow your most intense obsessions mercilessly."
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      
      <AuthorHero 
        name="Franz Kafka"
        quote="A book must be the axe for the frozen sea inside us"
        years="1883 – 1924"
      />
      
      <BiographySection 
        title="Life & Legacy"
        content={biography}
      />
      
      <WorksSection works={works} />
      
      <QuotesSection quotes={quotes} />
    </div>
  );
};

export default Kafka;