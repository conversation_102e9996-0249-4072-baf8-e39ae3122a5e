interface BiographySectionProps {
  title: string;
  content: string[];
}

const BiographySection = ({ title, content }: BiographySectionProps) => {
  return (
    <section className="py-16 lg:py-24">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 className="font-heading text-heading font-semibold text-primary mb-8 text-center animate-fade-in">
          {title}
        </h2>
        
        <div className="prose prose-lg max-w-none">
          {content.map((paragraph, index) => (
            <p 
              key={index}
              className={`font-body text-body leading-relaxed text-foreground/90 mb-6 animate-fade-in-up ${
                index === 0 ? 'first-letter:text-6xl first-letter:font-heading first-letter:text-primary first-letter:float-left first-letter:mr-2 first-letter:mt-1 first-letter:leading-none' : ''
              }`}
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              {paragraph}
            </p>
          ))}
        </div>
      </div>
    </section>
  );
};

export default BiographySection;