import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";
import Authors from "./pages/Authors";
import Quotes from "./pages/Quotes";
import About from "./pages/About";
import Search from "./pages/Search";
import <PERSON><PERSON><PERSON><PERSON> from "./pages/author/<PERSON><PERSON><PERSON><PERSON>";
import Kafka from "./pages/author/Kafka";
import Chekhov from "./pages/author/Chekhov";
import Wilde from "./pages/author/Wilde";
import Plath from "./pages/author/Plath";
import Shakespeare from "./pages/author/Shakespeare";
import <PERSON> from "./pages/author/<PERSON>";
import <PERSON> from "./pages/author/<PERSON>";
import Woolf from "./pages/author/Woolf";
import Rilke from "./pages/author/Rilke";
import Tolstoy from "./pages/author/Tolstoy";
import Borges from "./pages/author/Borges";
import <PERSON>us from "./pages/author/Camus";
import Neruda from "./pages/author/Neruda";
import BasilDaeren from "./pages/author/BasilDaeren";
import Joyce from "./pages/author/<PERSON>";
import <PERSON> from "./pages/author/<PERSON>";
import Hemingway from "./pages/author/Hemingway";
import Baldwin from "./pages/author/<PERSON>";
import Morrison from "./pages/author/<PERSON>";
import GarciaMarquez from "./pages/author/GarciaMarquez";
import Orwell from "./pages/author/Orwell";
import Lorca from "./pages/author/Lorca";
import Mishima from "./pages/author/Mishima";
import Pessoa from "./pages/author/Pessoa";
import Rumi from "./pages/author/Rumi";
import Hughes from "./pages/author/Hughes";
import Atwood from "./pages/author/Atwood";
import Achebe from "./pages/author/Achebe";
import Whitman from "./pages/author/Whitman";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <Routes>
          <Route path="/" element={<Index />} />
          <Route path="/authors" element={<Authors />} />
          <Route path="/quotes" element={<Quotes />} />
          <Route path="/about" element={<About />} />
          <Route path="/search" element={<Search />} />
          <Route path="/author/dostoevsky" element={<Dostoevsky />} />
          <Route path="/author/kafka" element={<Kafka />} />
          <Route path="/author/chekhov" element={<Chekhov />} />
          <Route path="/author/wilde" element={<Wilde />} />
          <Route path="/author/plath" element={<Plath />} />
          <Route path="/author/shakespeare" element={<Shakespeare />} />
          <Route path="/author/dickinson" element={<Dickinson />} />
          <Route path="/author/poe" element={<Poe />} />
          <Route path="/author/woolf" element={<Woolf />} />
          <Route path="/author/rilke" element={<Rilke />} />
          <Route path="/author/tolstoy" element={<Tolstoy />} />
          <Route path="/author/borges" element={<Borges />} />
          <Route path="/author/camus" element={<Camus />} />
          <Route path="/author/neruda" element={<Neruda />} />
          <Route path="/author/basil-daeren" element={<BasilDaeren />} />
          <Route path="/author/joyce" element={<Joyce />} />
          <Route path="/author/austen" element={<Austen />} />
          <Route path="/author/hemingway" element={<Hemingway />} />
          <Route path="/author/baldwin" element={<Baldwin />} />
          <Route path="/author/morrison" element={<Morrison />} />
          <Route path="/author/garcia-marquez" element={<GarciaMarquez />} />
          <Route path="/author/orwell" element={<Orwell />} />
          <Route path="/author/lorca" element={<Lorca />} />
          <Route path="/author/mishima" element={<Mishima />} />
          <Route path="/author/pessoa" element={<Pessoa />} />
          <Route path="/author/rumi" element={<Rumi />} />
          <Route path="/author/hughes" element={<Hughes />} />
          <Route path="/author/atwood" element={<Atwood />} />
          <Route path="/author/achebe" element={<Achebe />} />
          <Route path="/author/whitman" element={<Whitman />} />
          {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
          <Route path="*" element={<NotFound />} />
        </Routes>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
