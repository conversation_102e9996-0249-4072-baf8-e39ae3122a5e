interface AuthorHeroProps {
  name: string;
  quote: string;
  years: string;
}

const AuthorHero = ({ name, quote, years }: AuthorHeroProps) => {
  return (
    <section className="relative py-20 lg:py-32 bg-gradient-to-br from-background via-muted/30 to-accent/20">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        {/* Author Name */}
        <h1 className="font-heading text-hero font-bold text-primary mb-6 animate-fade-in tracking-tight">
          {name}
        </h1>
        
        {/* Years */}
        <p className="font-body text-lg text-muted-foreground mb-8 animate-fade-in-delay tracking-wide">
          {years}
        </p>
        
        {/* Quote */}
        <blockquote className="relative animate-fade-in-up">
          <div className="absolute -top-4 -left-4 text-6xl text-accent/30 font-heading leading-none">
            "
          </div>
          <div className="absolute -bottom-8 -right-4 text-6xl text-accent/30 font-heading leading-none">
            "
          </div>
          <p className="font-literary text-xl lg:text-2xl italic text-foreground/90 leading-relaxed max-w-3xl mx-auto px-8 py-4">
            {quote}
          </p>
        </blockquote>
      </div>
      
      {/* Decorative elements */}
      <div className="absolute top-1/2 left-4 w-1 h-16 bg-gradient-to-b from-primary/20 to-transparent rounded-full transform -translate-y-1/2"></div>
      <div className="absolute top-1/2 right-4 w-1 h-16 bg-gradient-to-b from-primary/20 to-transparent rounded-full transform -translate-y-1/2"></div>
    </section>
  );
};

export default AuthorHero;