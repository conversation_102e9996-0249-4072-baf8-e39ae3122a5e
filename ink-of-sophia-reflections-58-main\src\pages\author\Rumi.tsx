import Navigation from "@/components/Navigation";
import AuthorHero from "@/components/AuthorHero";
import BiographySection from "@/components/BiographySection";
import WorksSection from "@/components/WorksSection";
import QuotesSection from "@/components/QuotesSection";

export default function <PERSON><PERSON>() {
  const biography = [
    "<PERSON><PERSON><PERSON> <PERSON><PERSON> (1207-1273) was a 13th-century Persian poet, Islamic scholar, theologian, and Sufi mystic whose spiritual and philosophical teachings transcend cultural and religious boundaries. Born in present-day Afghanistan, his family fled the Mongol invasions when he was young.",
    "<PERSON><PERSON>'s life was transformed by his encounter with the wandering dervish <PERSON><PERSON><PERSON> of Tabriz, whose friendship sparked the mystical awakening that would inspire <PERSON><PERSON>'s greatest poetry. After <PERSON><PERSON><PERSON>'s mysterious disappearance, <PERSON><PERSON> channeled his grief and spiritual longing into thousands of verses celebrating divine love.",
    "His masterwork, the 'Masnavi,' is considered one of the greatest works of mystical poetry, often called 'the Quran in Persian.' <PERSON><PERSON>'s poetry explores themes of divine love, spiritual transformation, and the unity of all existence, making him one of the most widely read poets in the world today."
  ];

  const works = [
    { title: "Masnavi", year: 1258, description: "Epic spiritual poem consisting of six books of mystical teachings" },
    { title: "<PERSON>wan-<PERSON>ham<PERSON>-e <PERSON>", year: 1244, description: "Collection of lyrical poems inspired by his spiritual companion" },
    { title: "<PERSON><PERSON>hi", year: 1250, description: "Prose work containing discourses and teachings" },
    { title: "Majalis-e Sab'a", year: 1260, description: "Seven sermons on spiritual topics" },
    { title: "Maktubat", year: 1265, description: "Collection of letters to disciples and friends" }
  ];

  const quotes = [
    { text: "Out beyond ideas of wrongdoing and rightdoing there is a field. I'll meet you there." },
    { text: "The wound is the place where the Light enters you." },
    { text: "Let yourself be silently drawn by the strange pull of what you really love. It will not lead you astray." }
  ];

  return (
    <div className="min-h-screen bg-cream">
      <Navigation />
      <AuthorHero 
        name="Jalal ad-Din Rumi" 
        quote="Yesterday I was clever, so I wanted to change the world. Today I am wise, so I am changing myself." 
        years="1207-1273" 
      />
      <BiographySection title="Biography" content={biography} />
      <WorksSection works={works} />
      <QuotesSection quotes={quotes} />
    </div>
  );
}