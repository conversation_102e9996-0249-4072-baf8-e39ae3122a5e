import Navigation from "@/components/Navigation";
import AuthorHero from "@/components/AuthorHero";
import BiographySection from "@/components/BiographySection";
import WorksSection from "@/components/WorksSection";
import QuotesSection from "@/components/QuotesSection";

const Plath = () => {
  const biography = [
    "<PERSON> was born in Boston in 1932 to a German immigrant father who was a professor of entomology and a mother who was a teacher. Her father's death when she was eight profoundly affected her, becoming a recurring theme in her poetry. An exceptional student, she attended Smith College on scholarship, where she excelled academically while struggling with severe depression.",
    "After a suicide attempt during college, <PERSON><PERSON><PERSON> underwent electroconvulsive therapy, an experience that would later inform her semi-autobiographical novel 'The Bell Jar.' She won a Fulbright scholarship to Cambridge University, where she met and married fellow poet <PERSON> in 1956. Their tempestuous relationship would deeply influence her work and personal life.",
    "<PERSON><PERSON><PERSON>'s poetry evolved from conventional forms to the powerful, confessional style that made her famous. Her final poems, written in the months before her death in 1963, are considered among the finest in the English language. Though she died by suicide at 30, her posthumously published 'Ariel' established her as one of the most important poets of the 20th century, earning her a Pulitzer Prize."
  ];

  const works = [
    {
      title: "<PERSON>",
      year: 1965,
      description: "Her most famous collection, containing some of her most powerful and disturbing poems about death, rebirth, and identity."
    },
    {
      title: "The <PERSON> <PERSON>ar",
      year: 1963,
      description: "Her semi-autobiographical novel about a young woman's descent into mental illness, a classic of feminist literature."
    },
    {
      title: "The Colossus",
      year: 1960,
      description: "Her first poetry collection, showing her technical mastery and the development of her distinctive voice."
    },
    {
      title: "Crossing the Water",
      year: 1971,
      description: "Transitional poems that bridge her early work and the fierce final poems of her career."
    },
    {
      title: "Winter Trees",
      year: 1971,
      description: "Late poems that explore themes of motherhood, nature, and psychological states."
    }
  ];

  const quotes = [
    {
      text: "The worst enemy to creativity is self-doubt.",
    },
    {
      text: "I took a deep breath and listened to the old brag of my heart. I am, I am, I am.",
      source: "The Bell Jar"
    },
    {
      text: "Everything in life is writable about if you have the outgoing guts to do it, and the imagination to improvise."
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      
      <AuthorHero 
        name="Sylvia Plath"
        quote="I took a deep breath and listened to the old brag of my heart. I am, I am, I am"
        years="1932 – 1963"
      />
      
      <BiographySection 
        title="Life & Legacy"
        content={biography}
      />
      
      <WorksSection works={works} />
      
      <QuotesSection quotes={quotes} />
    </div>
  );
};

export default Plath;