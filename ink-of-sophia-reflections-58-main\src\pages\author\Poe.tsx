import Navigation from "@/components/Navigation";
import AuthorHero from "@/components/AuthorHero";
import BiographySection from "@/components/BiographySection";
import WorksSection from "@/components/WorksSection";
import QuotesSection from "@/components/QuotesSection";

export default function Poe() {
  const biography = [
    "<PERSON> (1809-1849) was an American writer, poet, editor, and literary critic best known for his tales of mystery and the macabre. Born in Boston to actors, he became an orphan at a young age and was taken in by <PERSON>, though never formally adopted.",
    "<PERSON> is credited with inventing the detective fiction genre and contributing significantly to the emerging science fiction genre. His work often explored themes of death, decay, and lost love, reflecting his own troubled life marked by alcoholism, financial difficulties, and personal losses.",
    "Despite his relatively short life and limited commercial success during his lifetime, <PERSON>'s influence on literature has been immense. His critical theories about poetry and short stories have shaped literary criticism, and his dark, atmospheric tales continue to captivate readers worldwide."
  ];

  const works = [
    { title: "The Raven and Other Poems", year: 1845, description: "Collection featuring his most famous poem" },
    { title: "Tales of the Grotesque and Arabesque", year: 1840, description: "First collection of short stories" },
    { title: "The Murders in the Rue Morgue", year: 1841, description: "Considered the first modern detective story" },
    { title: "The Tell-Tale Heart", year: 1843, description: "Classic tale of guilt and madness" },
    { title: "The Fall of the House of Usher", year: 1839, description: "Gothic masterpiece of atmospheric horror" }
  ];

  const quotes = [
    { text: "All that we see or seem is but a dream within a dream." },
    { text: "I became insane, with long intervals of horrible sanity." },
    { text: "The death of a beautiful woman is, unquestionably, the most poetical topic in the world." }
  ];

  return (
    <div className="min-h-screen bg-cream">
      <Navigation />
      <AuthorHero 
        name="Edgar Allan Poe" 
        quote="Deep into that darkness peering, long I stood there wondering, fearing." 
        years="1809-1849" 
      />
      <BiographySection title="Biography" content={biography} />
      <WorksSection works={works} />
      <QuotesSection quotes={quotes} />
    </div>
  );
}