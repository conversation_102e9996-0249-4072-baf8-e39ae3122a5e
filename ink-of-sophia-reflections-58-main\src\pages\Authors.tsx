import { Link } from "react-router-dom";
import Navigation from "@/components/Navigation";

const authors = [
  {
    slug: "<PERSON><PERSON><PERSON><PERSON>",
    name: "<PERSON><PERSON><PERSON>",
    years: "1821–1881",
    nationality: "Russian",
    description: "Master of psychological realism and existential philosophy"
  },
  {
    slug: "kafka",
    name: "<PERSON>",
    years: "1883–1924", 
    nationality: "Bohemian",
    description: "Pioneer of existential literature and absurdist fiction"
  },
  {
    slug: "chekhov",
    name: "<PERSON>",
    years: "1860–1904",
    nationality: "Russian", 
    description: "Master of the modern short story and playwright"
  },
  {
    slug: "wilde",
    name: "<PERSON>",
    years: "1854–1900",
    nationality: "Irish",
    description: "Wit, aesthete, and champion of art for art's sake"
  },
  {
    slug: "plath",
    name: "<PERSON>",
    years: "1932–1963",
    nationality: "American",
    description: "Confessional poet of raw emotional intensity"
  },
  {
    slug: "shakespeare",
    name: "<PERSON>",
    years: "1564–1616",
    nationality: "English",
    description: "The Bard of Avon, greatest writer in the English language"
  },
  {
    slug: "dickinson",
    name: "<PERSON>",
    years: "1830–1886",
    nationality: "American",
    description: "Reclusive poet who revolutionized American verse"
  },
  {
    slug: "poe",
    name: "Edgar Allan Poe",
    years: "1809–1849",
    nationality: "American",
    description: "Master of Gothic horror and detective fiction"
  },
  {
    slug: "woolf",
    name: "Virginia Woolf",
    years: "1882–1941",
    nationality: "English",
    description: "Modernist pioneer and stream-of-consciousness innovator"
  },
  {
    slug: "rilke",
    name: "Rainer Maria Rilke",
    years: "1875–1926",
    nationality: "Austrian",
    description: "Lyrical poet of spiritual and existential themes"
  },
  {
    slug: "tolstoy",
    name: "Leo Tolstoy",
    years: "1828–1910",
    nationality: "Russian",
    description: "Epic novelist and moral philosopher"
  },
  {
    slug: "borges",
    name: "Jorge Luis Borges",
    years: "1899–1986",
    nationality: "Argentine",
    description: "Master of labyrinths, mirrors, and infinite literature"
  },
  {
    slug: "camus",
    name: "Albert Camus",
    years: "1913–1960",
    nationality: "French-Algerian",
    description: "Absurdist philosopher and Nobel laureate"
  },
  {
    slug: "neruda",
    name: "Pablo Neruda",
    years: "1904–1973",
    nationality: "Chilean",
    description: "Passionate poet of love, politics, and nature"
  },
  {
    slug: "joyce",
    name: "James Joyce",
    years: "1882–1941",
    nationality: "Irish",
    description: "Revolutionary modernist who transformed the novel"
  },
  {
    slug: "austen",
    name: "Jane Austen",
    years: "1775–1817",
    nationality: "English",
    description: "Witty chronicler of Regency society and manners"
  },
  {
    slug: "hemingway",
    name: "Ernest Hemingway",
    years: "1899–1961",
    nationality: "American",
    description: "Minimalist master of the lost generation"
  },
  {
    slug: "baldwin",
    name: "James Baldwin",
    years: "1924–1987",
    nationality: "American",
    description: "Powerful voice on race, sexuality, and social justice"
  },
  {
    slug: "morrison",
    name: "Toni Morrison",
    years: "1931–2019",
    nationality: "American",
    description: "Nobel laureate exploring African American experience"
  },
  {
    slug: "garcia-marquez",
    name: "Gabriel García Márquez",
    years: "1927–2014",
    nationality: "Colombian",
    description: "Master of magical realism and Latin American literature"
  },
  {
    slug: "orwell",
    name: "George Orwell",
    years: "1903–1950",
    nationality: "English",
    description: "Dystopian visionary and political satirist"
  },
  {
    slug: "lorca",
    name: "Federico García Lorca",
    years: "1898–1936",
    nationality: "Spanish",
    description: "Surrealist poet and playwright of Andalusian soul"
  },
  {
    slug: "mishima",
    name: "Yukio Mishima",
    years: "1925–1970",
    nationality: "Japanese",
    description: "Controversial novelist exploring beauty, death, and tradition"
  },
  {
    slug: "pessoa",
    name: "Fernando Pessoa",
    years: "1888–1935",
    nationality: "Portuguese",
    description: "Poet of multiple personalities and modernist innovation"
  },
  {
    slug: "basil-daeren",
    name: "Basil Daeren",
    years: "1887–1924",
    nationality: "British",
    description: "Mystical poet bridging Victorian and modernist traditions"
  },
  {
    slug: "rumi",
    name: "Jalal ad-Din Rumi",
    years: "1207–1273",
    nationality: "Persian",
    description: "Sufi mystic poet of divine love and spiritual ecstasy"
  },
  {
    slug: "hughes",
    name: "Langston Hughes",
    years: "1901–1967",
    nationality: "American",
    description: "Voice of the Harlem Renaissance and jazz poetry"
  },
  {
    slug: "atwood",
    name: "Margaret Atwood",
    years: "1939–",
    nationality: "Canadian",
    description: "Speculative fiction pioneer and feminist literary icon"
  },
  {
    slug: "achebe",
    name: "Chinua Achebe",
    years: "1930–2013",
    nationality: "Nigerian",
    description: "Postcolonial author who gave voice to African literature"
  },
  {
    slug: "whitman",
    name: "Walt Whitman",
    years: "1819–1892",
    nationality: "American",
    description: "Democratic poet celebrating the American spirit"
  }
];

const Authors = () => {
  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      
      <main className="py-16 lg:py-24">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16 animate-fade-in">
            <h1 className="font-heading text-hero font-bold text-primary mb-6 tracking-tight">
              Literary Voices
            </h1>
            <p className="font-body text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Explore the lives and works of history's most profound literary minds, 
              from celebrated masters to forgotten voices awaiting rediscovery.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {authors.map((author, index) => (
              <Link
                key={author.slug}
                to={`/author/${author.slug}`}
                className="group animate-scale-in"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="bg-card rounded-lg p-8 shadow-soft hover:shadow-elegant transition-all duration-300 border border-border/50 group-hover:border-primary/20">
                  <h3 className="font-heading text-xl font-semibold text-primary mb-2 group-hover:text-primary/80 transition-colors">
                    {author.name}
                  </h3>
                  <p className="font-body text-sm text-muted-foreground mb-3">
                    {author.years} • {author.nationality}
                  </p>
                  <p className="font-body text-sm text-foreground/80 leading-relaxed">
                    {author.description}
                  </p>
                  
                  <div className="mt-6 flex items-center text-primary group-hover:text-primary/80 transition-colors">
                    <span className="font-body text-sm font-medium">Read More</span>
                    <svg className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </main>
    </div>
  );
};

export default Authors;