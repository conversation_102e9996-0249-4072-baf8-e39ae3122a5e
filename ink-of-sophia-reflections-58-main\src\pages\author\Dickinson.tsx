import Navigation from "@/components/Navigation";
import AuthorHero from "@/components/AuthorHero";
import BiographySection from "@/components/BiographySection";
import WorksSection from "@/components/WorksSection";
import QuotesSection from "@/components/QuotesSection";

export default function Dickinson() {
  const biography = [
    "<PERSON> (1830-1886) was an American poet who lived most of her life in almost total seclusion in Amherst, Massachusetts. Known for her reclusive nature, she wrote nearly 1,800 poems, though fewer than a dozen were published during her lifetime.",
    "Her poetry is characterized by short lines, slant rhyme, unconventional capitalization and punctuation, and themes of death, immortality, and nature. Her unique style and profound insights into the human condition have made her one of the most important American poets.",
    "<PERSON>'s work was largely unknown during her lifetime, but her sister-in-law discovered her poems after her death and ensured their publication, revealing a voice of extraordinary power and originality that continues to resonate with readers today."
  ];

  const works = [
    { title: "Poems by <PERSON>", year: 1890, description: "First posthumous collection, edited by <PERSON>" },
    { title: "Poems: Second Series", year: 1891, description: "Additional poems published by family" },
    { title: "The Complete Poems of <PERSON>", year: 1955, description: "Comprehensive collection edited by <PERSON>" },
    { title: "The Manuscript Books of <PERSON>", year: 1981, description: "Facsimile edition preserving her original presentation" }
  ];

  const quotes = [
    { text: "I'm Nobody! Who are you? Are you - Nobody - Too? Then there's a pair of us!" },
    { text: "Hope is the thing with feathers that perches in the soul." },
    { text: "Because I could not stop for Death, He kindly stopped for me." }
  ];

  return (
    <div className="min-h-screen bg-cream">
      <Navigation />
      <AuthorHero 
        name="Emily <PERSON>" 
        quote="I dwell in possibility." 
        years="1830-1886" 
      />
      <BiographySection title="Biography" content={biography} />
      <WorksSection works={works} />
      <QuotesSection quotes={quotes} />
    </div>
  );
}