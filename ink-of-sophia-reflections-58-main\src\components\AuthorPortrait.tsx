interface AuthorPortraitProps {
  imageSrc: string;
  imageAlt: string;
  authorName: string;
}

const AuthorPortrait = ({ imageSrc, imageAlt, authorName }: AuthorPortraitProps) => {
  return (
    <section className="py-16 bg-gradient-to-br from-accent/10 to-muted/20">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div className="relative inline-block animate-scale-in">
          <div className="relative">
            <img 
              src={imageSrc}
              alt={imageAlt}
              className="w-64 h-80 md:w-80 md:h-96 object-cover rounded-lg shadow-elegant mx-auto filter grayscale hover:grayscale-0 transition-all duration-500"
            />
            
            {/* Decorative frame */}
            <div className="absolute inset-0 rounded-lg border-2 border-primary/20 pointer-events-none"></div>
            <div className="absolute -inset-2 rounded-lg border border-accent/30 pointer-events-none"></div>
          </div>
          
          {/* Caption */}
          <p className="font-body text-sm text-muted-foreground mt-4 italic">
            {authorName}
          </p>
        </div>
      </div>
    </section>
  );
};

export default AuthorPortrait;