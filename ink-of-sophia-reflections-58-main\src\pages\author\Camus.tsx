import Navigation from "@/components/Navigation";
import AuthorHero from "@/components/AuthorHero";
import BiographySection from "@/components/BiographySection";
import WorksSection from "@/components/WorksSection";
import QuotesSection from "@/components/QuotesSection";

export default function <PERSON><PERSON>() {
  const biography = [
    "<PERSON> (1913-1960) was a French-Algerian philosopher, author, and journalist who became one of the most significant thinkers of the 20th century. Born into poverty in colonial Algeria, his experiences there deeply influenced his worldview and writing.",
    "<PERSON><PERSON> is most closely associated with existentialism and absurdism, though he rejected the existentialist label. His philosophy centered on the concept of the absurd—the conflict between human desire for meaning and the meaningless silence of the universe.",
    "In 1957, at age 44, <PERSON><PERSON> became the second-youngest recipient of the Nobel Prize in Literature. His works explore themes of suicide, rebellion, and the human condition, advocating for living fully despite life's inherent meaninglessness."
  ];

  const works = [
    { title: "The Stranger", year: 1942, description: "Novel exploring alienation and the absurd" },
    { title: "The Plague", year: 1947, description: "Allegorical novel about a plague outbreak in Algeria" },
    { title: "The Myth of Sisyphus", year: 1942, description: "Philosophical essay on the absurd and suicide" },
    { title: "The Rebel", year: 1951, description: "Essay examining revolution and rebellion" },
    { title: "The Fall", year: 1956, description: "Philosophical novel told as a monologue" }
  ];

  const quotes = [
    { text: "In the midst of winter, I found there was, within me, an invincible summer." },
    { text: "The struggle itself toward the heights is enough to fill a man's heart." },
    { text: "There is but one truly serious philosophical problem, and that is suicide." }
  ];

  return (
    <div className="min-h-screen bg-cream">
      <Navigation />
      <AuthorHero 
        name="Albert Camus" 
        quote="I rebel; therefore I exist." 
        years="1913-1960" 
      />
      <BiographySection title="Biography" content={biography} />
      <WorksSection works={works} />
      <QuotesSection quotes={quotes} />
    </div>
  );
}