import Navigation from "@/components/Navigation";

const quotes = [
  {
    text: "It is better to light a candle than curse the darkness.",
    author: "<PERSON>",
    category: "Inspiration"
  },
  {
    text: "The way to get started is to quit talking and begin doing.",
    author: "<PERSON> Disney",
    category: "Action"
  },
  {
    text: "Pain is inevitable. Suffering is optional.",
    author: "<PERSON><PERSON><PERSON>",
    category: "Philosophy"
  },
  {
    text: "A goal is not always meant to be reached, it often serves simply as something to aim at.",
    author: "<PERSON>",
    category: "Purpose"
  },
  {
    text: "The unexamined life is not worth living.",
    author: "Socrates",
    category: "Philosophy"
  },
  {
    text: "In the depth of winter, I finally learned that there was in me an invincible summer.",
    author: "<PERSON>",
    category: "Resilience"
  },
  {
    text: "What we know is a drop, what we don't know is an ocean.",
    author: "<PERSON>",
    category: "Knowledge"
  },
  {
    text: "Be yourself; everyone else is already taken.",
    author: "<PERSON>",
    category: "Authenticity"
  },
  {
    text: "I can resist everything except temptation.",
    author: "<PERSON>",
    category: "Human Nature"
  },
  {
    text: "The best time to plant a tree was 20 years ago. The second best time is now.",
    author: "Chinese Proverb",
    category: "Action"
  },
  {
    text: "Mad, bad, and dangerous to know.",
    author: "Lady <PERSON>",
    category: "Passion"
  },
  {
    text: "I have learned throughout my life as a composer chiefly through my mistakes and pursuits of false assumptions, not by my exposure to founts of wisdom and knowledge.",
    author: "Igor Stravinsky",
    category: "Learning"
  }
];

const categories = ["All", "Philosophy", "Inspiration", "Action", "Purpose", "Resilience", "Knowledge", "Authenticity", "Human Nature", "Passion", "Learning"];

export default function Quotes() {
  return (
    <div className="min-h-screen bg-cream">
      <Navigation />
      
      <div className="container mx-auto px-4 py-12">
        <div className="max-w-4xl mx-auto">
          <header className="text-center mb-16 animate-fade-in">
            <h1 className="font-display text-4xl md:text-6xl text-charcoal mb-6">
              Literary Quotes
            </h1>
            <p className="text-lg text-brown max-w-2xl mx-auto leading-relaxed">
              A curated collection of wisdom from the great minds of literature, philosophy, and art.
            </p>
          </header>

          <div className="grid gap-8 md:gap-12">
            {quotes.map((quote, index) => (
              <div 
                key={index}
                className="animate-fade-in bg-white/60 backdrop-blur-sm rounded-lg p-8 shadow-soft border border-white/20"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <blockquote className="text-center">
                  <p className="text-xl md:text-2xl text-charcoal font-light leading-relaxed mb-6 italic">
                    "{quote.text}"
                  </p>
                  <footer>
                    <cite className="text-brown font-medium text-lg">
                      — {quote.author}
                    </cite>
                    <div className="mt-2">
                      <span className="inline-block px-3 py-1 bg-pale-pink/60 text-brown text-sm rounded-full">
                        {quote.category}
                      </span>
                    </div>
                  </footer>
                </blockquote>
              </div>
            ))}
          </div>

          <div className="text-center mt-16">
            <p className="text-brown italic">
              "The purpose of literature is to turn blood into ink." — T.S. Eliot
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}