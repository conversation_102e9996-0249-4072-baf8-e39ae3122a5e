import { useState } from "react";
import { Menu, X, Search } from "lucide-react";
import { Link } from "react-router-dom";

const Navigation = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <nav className="sticky top-0 z-50 bg-background/80 backdrop-blur-sm border-b border-border">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex-shrink-0">
            <Link to="/" className="font-heading text-xl font-semibold text-primary hover:text-primary/80 transition-colors">
              Ink of Sophia
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:block">
            <div className="ml-10 flex items-baseline space-x-8">
              <Link
                to="/"
                className="text-foreground hover:text-primary transition-colors duration-200 font-body text-sm tracking-wide relative group"
              >
                Home
                <span className="absolute inset-x-0 bottom-0 h-0.5 bg-primary transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></span>
              </Link>
              <Link
                to="/authors"
                className="text-foreground hover:text-primary transition-colors duration-200 font-body text-sm tracking-wide relative group"
              >
                Authors
                <span className="absolute inset-x-0 bottom-0 h-0.5 bg-primary transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></span>
              </Link>
              <Link
                to="/quotes"
                className="text-foreground hover:text-primary transition-colors duration-200 font-body text-sm tracking-wide relative group"
              >
                Quotes
                <span className="absolute inset-x-0 bottom-0 h-0.5 bg-primary transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></span>
              </Link>
              <Link
                to="/about"
                className="text-foreground hover:text-primary transition-colors duration-200 font-body text-sm tracking-wide relative group"
              >
                About
                <span className="absolute inset-x-0 bottom-0 h-0.5 bg-primary transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></span>
              </Link>
              <Link to="/search" className="text-muted-foreground hover:text-primary transition-colors">
                <Search className="h-4 w-4" />
              </Link>
            </div>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="text-foreground hover:text-primary transition-colors"
            >
              {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      {isMenuOpen && (
        <div className="md:hidden animate-fade-in">
          <div className="px-2 pt-2 pb-3 space-y-1 bg-background border-t border-border">
            <Link
              to="/"
              className="block px-3 py-2 text-foreground hover:text-primary transition-colors font-body"
              onClick={() => setIsMenuOpen(false)}
            >
              Home
            </Link>
            <Link
              to="/authors"
              className="block px-3 py-2 text-foreground hover:text-primary transition-colors font-body"
              onClick={() => setIsMenuOpen(false)}
            >
              Authors
            </Link>
            <Link
              to="/quotes"
              className="block px-3 py-2 text-foreground hover:text-primary transition-colors font-body"
              onClick={() => setIsMenuOpen(false)}
            >
              Quotes
            </Link>
            <Link
              to="/about"
              className="block px-3 py-2 text-foreground hover:text-primary transition-colors font-body"
              onClick={() => setIsMenuOpen(false)}
            >
              About
            </Link>
            <Link
              to="/search"
              className="block px-3 py-2 text-foreground hover:text-primary transition-colors font-body"
              onClick={() => setIsMenuOpen(false)}
            >
              Search
            </Link>
          </div>
        </div>
      )}
    </nav>
  );
};

export default Navigation;