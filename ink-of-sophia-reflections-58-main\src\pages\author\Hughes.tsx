import Navigation from "@/components/Navigation";
import AuthorHero from "@/components/AuthorHero";
import BiographySection from "@/components/BiographySection";
import WorksSection from "@/components/WorksSection";
import QuotesSection from "@/components/QuotesSection";

export default function <PERSON>() {
  const biography = [
    "<PERSON><PERSON> (1901-1967) was an American poet, social activist, novelist, playwright, and columnist who became one of the most important figures of the Harlem Renaissance. Born in Joplin, Missouri, he moved frequently during his childhood before settling in Harlem, New York.",
    "<PERSON> was one of the first writers to celebrate African American culture and experience in his work, incorporating the rhythms of jazz and blues into his poetry. His writing addressed the struggles and joys of working-class African Americans with both dignity and artistic innovation.",
    "Beyond poetry, <PERSON> wrote novels, short stories, and plays that explored themes of racial identity, social justice, and the American Dream. His commitment to representing the voice of common people and his belief in the power of art to create social change made him a defining voice of his generation."
  ];

  const works = [
    { title: "The Weary Blues", year: 1926, description: "Debut poetry collection celebrating jazz culture and African American life" },
    { title: "Not Without Laughter", year: 1930, description: "Novel about a young African American boy growing up in Kansas" },
    { title: "The Ways of White Folks", year: 1934, description: "Short story collection examining racial tensions in America" },
    { title: "Montage of a Dream Deferred", year: 1951, description: "Poetry collection exploring the deferred dreams of Harlem residents" },
    { title: "Simple Speaks His Mind", year: 1950, description: "Collected stories featuring his beloved character Jesse B. Semple" }
  ];

  const quotes = [
    { text: "Hold fast to dreams, for if dreams die, life is a broken-winged bird that cannot fly." },
    { text: "What happens to a dream deferred? Does it dry up like a raisin in the sun?" },
    { text: "I, too, am America." }
  ];

  return (
    <div className="min-h-screen bg-cream">
      <Navigation />
      <AuthorHero 
        name="Langston Hughes" 
        quote="My soul has grown deep like the rivers." 
        years="1901-1967" 
      />
      <BiographySection title="Biography" content={biography} />
      <WorksSection works={works} />
      <QuotesSection quotes={quotes} />
    </div>
  );
}