import Navigation from "@/components/Navigation";
import AuthorHero from "@/components/AuthorHero";
import BiographySection from "@/components/BiographySection";
import WorksSection from "@/components/WorksSection";
import QuotesSection from "@/components/QuotesSection";

const Shakespeare = () => {
  const biography = [
    "<PERSON> was born in Stratford-upon-Avon in 1564 to <PERSON>, a glove-maker and alderman, and <PERSON>, from a well-to-do farming family. <PERSON> is known about his early education, though he likely attended the King's New School in Stratford, where he would have learned Latin and classical literature that would later influence his plays.",
    "In 1582, <PERSON> married <PERSON>, eight years his senior, and they had three children together. By the early 1590s, he had moved to London and established himself as both an actor and playwright. He became a founding member of the Lord Chamberlain's Men (later the King's Men), one of the leading theatrical companies of the day.",
    "<PERSON>'s career spanned roughly 25 years, during which he wrote approximately 37 plays and 154 sonnets. His works encompass tragedies, comedies, and histories that explore the full range of human experience with unparalleled psychological insight and poetic beauty. He died in 1616, leaving behind a body of work that has influenced virtually every subsequent writer in the English language and established him as the greatest playwright in literary history."
  ];

  const works = [
    {
      title: "Hamlet",
      year: 1601,
      description: "The tragic tale of the <PERSON> of <PERSON>'s quest for revenge, featuring the most famous soliloquy in English literature."
    },
    {
      title: "<PERSON> and <PERSON>",
      year: 1597,
      description: "The archetypal story of young love destroyed by family feuds, one of the most performed plays in history."
    },
    {
      title: "Macbeth",
      year: 1606,
      description: "A dark tragedy of ambition and guilt, exploring the psychological consequences of unchecked power."
    },
    {
      title: "A Midsummer Night's Dream",
      year: 1596,
      description: "A magical comedy weaving together multiple love stories with fairy enchantment and theatrical illusion."
    },
    {
      title: "King Lear",
      year: 1606,
      description: "A brutal tragedy of family betrayal and the abdication of power, considered by many his greatest work."
    }
  ];

  const quotes = [
    {
      text: "To be, or not to be, that is the question.",
      source: "Hamlet"
    },
    {
      text: "All the world's a stage, and all the men and women merely players.",
      source: "As You Like It"
    },
    {
      text: "The course of true love never did run smooth.",
      source: "A Midsummer Night's Dream"
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      
      <AuthorHero 
        name="William Shakespeare"
        quote="All the world's a stage, and all the men and women merely players"
        years="1564 – 1616"
      />
      
      <BiographySection 
        title="Life & Legacy"
        content={biography}
      />
      
      <WorksSection works={works} />
      
      <QuotesSection quotes={quotes} />
    </div>
  );
};

export default Shakespeare;