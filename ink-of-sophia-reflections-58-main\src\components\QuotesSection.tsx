interface Quote {
  text: string;
  source?: string;
}

interface QuotesSectionProps {
  quotes: Quote[];
}

const QuotesSection = ({ quotes }: QuotesSectionProps) => {
  return (
    <section className="py-16 lg:py-24 bg-gradient-to-b from-background to-secondary/10">
      <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16 animate-fade-in">
          <h2 className="font-heading text-heading font-bold text-primary mb-4 tracking-tight">
            Literary Reflections
          </h2>
          <div className="w-16 h-1 bg-gradient-to-r from-primary via-accent to-primary mx-auto rounded-full opacity-60"></div>
        </div>
        
        <div className="space-y-8 lg:space-y-12">
          {quotes.map((quote, index) => (
            <div 
              key={index}
              className="group relative animate-fade-in-up"
              style={{ animationDelay: `${index * 0.2}s` }}
            >
              <div className="relative bg-gradient-to-br from-card/90 via-card/70 to-card/80 backdrop-blur-sm rounded-3xl p-8 lg:p-12 shadow-soft hover:shadow-elegant transition-all duration-500 border border-border/20 hover:border-primary/15 overflow-hidden">
                {/* Decorative elements */}
                <div className="absolute -top-4 -left-4 w-24 h-24 bg-gradient-to-br from-accent/20 to-primary/10 rounded-full blur-xl opacity-60 group-hover:opacity-80 transition-opacity duration-500"></div>
                <div className="absolute -bottom-4 -right-4 w-32 h-32 bg-gradient-to-tl from-primary/10 to-accent/20 rounded-full blur-xl opacity-60 group-hover:opacity-80 transition-opacity duration-500"></div>
                
                {/* Quote marks */}
                <div className="absolute top-6 left-8 text-6xl lg:text-7xl text-accent/30 font-serif leading-none">
                  "
                </div>
                <div className="absolute bottom-6 right-8 text-6xl lg:text-7xl text-accent/30 font-serif leading-none">
                  "
                </div>
                
                {/* Quote content */}
                <blockquote className="relative z-10">
                  <p className="font-heading text-lg lg:text-xl text-foreground leading-relaxed text-center px-8 lg:px-16 italic group-hover:text-foreground/95 transition-colors duration-300">
                    {quote.text}
                  </p>
                  
                  {quote.source && (
                    <footer className="mt-8 text-center">
                      <cite className="font-body text-sm text-muted-foreground/80">
                        — {quote.source}
                      </cite>
                    </footer>
                  )}
                </blockquote>
                
                {/* Subtle inner glow */}
                <div className="absolute inset-4 bg-gradient-to-br from-accent/5 via-transparent to-primary/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default QuotesSection;