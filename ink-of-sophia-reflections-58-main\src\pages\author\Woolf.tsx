import Navigation from "@/components/Navigation";
import AuthorHero from "@/components/AuthorHero";
import BiographySection from "@/components/BiographySection";
import WorksSection from "@/components/WorksSection";
import QuotesSection from "@/components/QuotesSection";

export default function Woolf() {
  const biography = [
    "<PERSON> (1882-1941) was an English writer and one of the foremost modernist literary figures of the twentieth century. Born into a literary family in London, she was largely self-educated but became one of the most innovative writers of her time.",
    "<PERSON><PERSON><PERSON> was a central figure in the Bloomsbury Group of intellectuals and artists. She pioneered stream-of-consciousness narrative techniques and explored themes of time, memory, and the inner lives of characters. Her work often examined the role of women in society and the nature of consciousness.",
    "Throughout her life, <PERSON><PERSON><PERSON> struggled with mental illness, which both tormented and informed her writing. Her novels broke new ground in psychological realism and narrative technique, influencing generations of writers and establishing her as a key figure in feminist literature."
  ];

  const works = [
    { title: "Mrs. <PERSON><PERSON>", year: 1925, description: "Modernist novel following a single day in London" },
    { title: "To the Lighthouse", year: 1927, description: "Experimental novel exploring time and memory" },
    { title: "Orlando", year: 1928, description: "Fantastical biography spanning centuries" },
    { title: "The Waves", year: 1931, description: "Highly experimental novel told through soliloquies" },
    { title: "A Room of One's Own", year: 1929, description: "Extended essay on women and fiction" }
  ];

  const quotes = [
    { text: "For most of history, Anonymous was a woman." },
    { text: "Yet it is in our idleness, in our dreams, that the submerged truth sometimes comes to the top." },
    { text: "A woman must have money and a room of her own if she is to write fiction." }
  ];

  return (
    <div className="min-h-screen bg-cream">
      <Navigation />
      <AuthorHero 
        name="Virginia Woolf" 
        quote="What is the meaning of life? That was all—a simple question; one that tended to close in on one with years." 
        years="1882-1941" 
      />
      <BiographySection title="Biography" content={biography} />
      <WorksSection works={works} />
      <QuotesSection quotes={quotes} />
    </div>
  );
}