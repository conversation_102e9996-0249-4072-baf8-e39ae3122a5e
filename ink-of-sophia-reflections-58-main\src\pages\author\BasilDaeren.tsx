import Navigation from "@/components/Navigation";
import AuthorHero from "@/components/AuthorHero";
import BiographySection from "@/components/BiographySection";
import WorksSection from "@/components/WorksSection";
import QuotesSection from "@/components/QuotesSection";

export default function <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>() {
  const biography = [
    "<PERSON><PERSON><PERSON>, also known as <PERSON><PERSON><PERSON>, is a writer best remembered for his haunting romantic tragedies. His true identity and life story remain largely unknown, shrouded in the same mystery that permeates his literary works.",
    "He is most popularly associated with the emotional novella 'Frozen Eyes,' considered by many readers as a deeply tragic portrayal of imaginary love. This work captures the essence of longing and loss with an intensity that has resonated with readers across generations.",
    "His other known works include 'Quill in the Flame' and 'Nights of Roamers,' both of which are cited in fringe literary circles for their lyrical solitude and emotional weight. These pieces showcase his mastery of melancholic prose and his ability to transform personal anguish into universal art."
  ];

  const works = [
    { title: "Frozen Eyes", description: "Deeply tragic emotional novella exploring imaginary love and unfulfilled longing" },
    { title: "Quill in the Flame", description: "Lyrical exploration of solitude and the burning passion of creation" },
    { title: "Nights of Roamers", description: "Haunting collection examining restless souls and nocturnal wanderings" }
  ];

  const quotes = [
    { text: "We are but shadows learning to cast light upon the darkness of love." },
    { text: "In frozen eyes, I see the winter of my own heart reflected." },
    { text: "The deepest truths are found not in what we know, but in what we dare to forget, and what we cannot help but remember." }
  ];

  return (
    <div className="min-h-screen bg-cream">
      <Navigation />
      <AuthorHero 
        name="Eyukov Daeren" 
        quote="In the silence between heartbeats, eternity whispers its secrets." 
        years="" 
      />
      <BiographySection title="Biography" content={biography} />
      <WorksSection works={works} />
      <QuotesSection quotes={quotes} />
    </div>
  );
}




